-- <PERSON><PERSON><PERSON>
if SERVER then
    -- Paylaş<PERSON><PERSON> dosyaları ekle
    AddCSLuaFile("gang_system/shared/sh_gang_config.lua")
    AddCSLuaFile("gang_system/shared/sh_gang_shared.lua")
    AddCSLuaFile("gang_system/client/cl_gang_ui.lua")
    AddCSLuaFile("autorun/client/cl_gang_init.lua")
    
    -- <PERSON><PERSON><PERSON> dosyalarını yükle
    include("gang_system/shared/sh_gang_config.lua")
    include("gang_system/shared/sh_gang_shared.lua")
    include("gang_system/server/sv_gang_core.lua")
    include("gang_system/server/sv_gang_commands.lua")

    -- Test dosyasını yükle (geliştirme amaçlı)
    if file.Exists("test_gang_system.lua", "LUA") then
        include("test_gang_system.lua")
    end

    print("[Çete Sistemi] Sunucu tarafı yüklendi!")
end
