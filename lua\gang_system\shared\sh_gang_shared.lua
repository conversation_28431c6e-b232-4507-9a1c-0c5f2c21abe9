-- Paylaş<PERSON>lan Fonksiyonlar
GangSystem = GangSystem or {}

-- Oyuncunun çete oluşturma yetkisi var mı?
function GangSystem.CanCreateGang(ply)
    if not IsValid(ply) then return false end

    local job = ply:getDarkRPVar("job") or ply:Team()
    local jobTable = RPExtraTeams[job]

    -- <PERSON><PERSON><PERSON> JobTable yoksa, job ID'sini direkt kontrol et (Mayor gibi özel durumlar için)
    if not jobTable then
        local jobStr = string.lower(tostring(job))
        print("[Çete Debug] JobTable bulunamadı - Job ID: " .. tostring(job) .. ", String: '" .. jobStr .. "'")

        -- Job ID'sini direkt kontrol et
        local canCreate = GangSystem.Config.AllowedCreateJobs[jobStr] or
                         GangSystem.Config.AllowedCreateJobs[tostring(job)] or
                         false

        print("[Çete Debug] Direkt Job ID kontrolü - Çete oluşturabilir mi: " .. tostring(canCreate))
        return canCreate
    end

    local jobCommand = string.lower(jobTable.command or "")
    local jobName = string.lower(jobTable.name or "")

    print("[Çete Debug] Oyuncu: " .. ply:Nick() .. ", Job Command: '" .. jobCommand .. "', Job Name: '" .. jobName .. "'")

    -- Hem command hem de name'i kontrol et
    local canCreate = GangSystem.Config.AllowedCreateJobs[jobCommand] or
                     GangSystem.Config.AllowedCreateJobs[jobName] or
                     GangSystem.Config.AllowedCreateJobs[string.lower(jobTable.name or "")] or
                     false

    print("[Çete Debug] Çete oluşturabilir mi: " .. tostring(canCreate))

    return canCreate
end

-- Oyuncunun çeteye katılma yetkisi var mı?
function GangSystem.CanJoinGang(ply)
    if not IsValid(ply) then return false end

    local job = ply:getDarkRPVar("job") or ply:Team()
    local jobTable = RPExtraTeams[job]

    -- Eğer JobTable yoksa, job ID'sini direkt kontrol et
    if not jobTable then
        local jobStr = string.lower(tostring(job))
        return GangSystem.Config.AllowedJoinJobs[jobStr] or
               GangSystem.Config.AllowedJoinJobs[tostring(job)] or
               false
    end

    local jobCommand = string.lower(jobTable.command or "")
    local jobName = string.lower(jobTable.name or "")

    return GangSystem.Config.AllowedJoinJobs[jobCommand] or
           GangSystem.Config.AllowedJoinJobs[jobName] or
           false
end

-- Oyuncunun çetesi var mı?
function GangSystem.HasGang(ply)
    if not IsValid(ply) then return false end
    return ply.GangID and ply.GangID > 0
end

-- Çete ismini temizle
function GangSystem.CleanGangName(name)
    if not name then return "" end
    name = string.Trim(name)
    name = string.gsub(name, "[^%w%s]", "") -- Sadece harf, rakam ve boşluk
    return name
end

-- Çete ismi geçerli mi?
function GangSystem.IsValidGangName(name)
    if not name then return false end
    
    local cleanName = GangSystem.CleanGangName(name)
    local len = string.len(cleanName)
    
    return len >= GangSystem.Config.MinNameLength and len <= GangSystem.Config.MaxNameLength
end

-- Renk interpolasyonu
function GangSystem.LerpColor(frac, from, to)
    return Color(
        Lerp(frac, from.r, to.r),
        Lerp(frac, from.g, to.g),
        Lerp(frac, from.b, to.b),
        Lerp(frac, from.a or 255, to.a or 255)
    )
end

-- Modern gölge çizimi
function GangSystem.DrawShadow(x, y, w, h, blur, color)
    blur = blur or 5
    color = color or Color(0, 0, 0, 100)
    
    for i = 1, blur do
        surface.SetDrawColor(color.r, color.g, color.b, color.a / blur)
        surface.DrawRect(x + i, y + i, w, h)
    end
end

-- Modern buton çizimi
function GangSystem.DrawButton(x, y, w, h, text, color, hovered)
    local btnColor = hovered and GangSystem.LerpColor(0.2, color, Color(255, 255, 255)) or color
    
    -- Gölge
    GangSystem.DrawShadow(x, y, w, h, 3)
    
    -- Buton arka planı
    surface.SetDrawColor(btnColor)
    surface.DrawRect(x, y, w, h)
    
    -- Kenarlık
    surface.SetDrawColor(255, 255, 255, 50)
    surface.DrawOutlinedRect(x, y, w, h)
    
    -- Metin
    surface.SetFont("GangSystem.Button")
    local tw, th = surface.GetTextSize(text)
    surface.SetTextColor(255, 255, 255, 255)
    surface.SetTextPos(x + w/2 - tw/2, y + h/2 - th/2)
    surface.DrawText(text)
end
