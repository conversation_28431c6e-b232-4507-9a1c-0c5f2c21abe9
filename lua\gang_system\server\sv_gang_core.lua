-- <PERSON><PERSON><PERSON> (Veritabanı Olmadan)
GangSystem = GangSystem or {}
GangSystem.Gangs = {}
GangSystem.PlayerInvites = {}
GangSystem.NextGangID = 1

-- Oyuncu çete bilgilerini güncelle
local function UpdatePlayerGangInfo(ply)
    if not IsValid(ply) then return end
    
    local steamid = ply:SteamID()
    ply.GangID = nil
    ply.GangRank = nil
    
    for gangID, gang in pairs(GangSystem.Gangs) do
        if gang.members[steamid] then
            ply.GangID = gangID
            ply.GangRank = gang.members[steamid].rank
            break
        end
    end
end

-- Çete oluştur
function GangSystem.CreateGang(ply, name)
    if not IsValid(ply) then return false, "Geçersiz oyuncu!" end
    if not GangSystem.CanCreateGang(ply) then return false, GangSystem.Config.Messages.InvalidJob end
    if GangSystem.HasGang(ply) then return false, GangSystem.Config.Messages.AlreadyInGang end
    if not GangSystem.IsValidGangName(name) then return false, "Geçersiz çete ismi!" end
    if ply:getDarkRPVar("money") < GangSystem.Config.CreateCost then return false, GangSystem.Config.Messages.NotEnoughMoney end

    -- Çete sayısı kontrolü
    if table.Count(GangSystem.Gangs) >= GangSystem.Config.MaxGangs then
        return false, "Maksimum çete sayısına ulaşıldı!"
    end

    -- İsim kontrolü
    for _, gang in pairs(GangSystem.Gangs) do
        if string.lower(gang.name) == string.lower(name) then
            return false, "Bu isimde bir çete zaten var!"
        end
    end

    -- Parayı düş
    ply:addMoney(-GangSystem.Config.CreateCost)

    -- Bellekte oluştur (veritabanı yok)
    local steamid = ply:SteamID()
    local time = os.time()
    local gangID = GangSystem.NextGangID
    GangSystem.NextGangID = GangSystem.NextGangID + 1

    GangSystem.Gangs[gangID] = {
        id = gangID,
        name = name,
        leader = steamid,
        leaderPlayer = ply, -- Lider referansı
        created = time,
        money = 0,
        members = {
            [steamid] = {rank = 3, joined = time, player = ply}
        }
    }

    UpdatePlayerGangInfo(ply)

    print("[Çete Sistemi] " .. ply:Nick() .. " '" .. name .. "' çetesini oluşturdu!")

    return true, GangSystem.Config.Messages.GangCreated
end

-- Oyuncu bağlandığında
hook.Add("PlayerInitialSpawn", "GangSystem.PlayerSpawn", function(ply)
    timer.Simple(1, function()
        if IsValid(ply) then
            UpdatePlayerGangInfo(ply)
        end
    end)
end)

-- Çeteyi sil
function GangSystem.DeleteGang(gangID, reason)
    if not GangSystem.Gangs[gangID] then return false, GangSystem.Config.Messages.GangNotFound end

    local gang = GangSystem.Gangs[gangID]
    reason = reason or "Çete silindi!"

    -- Tüm üyelere bildir
    for steamid, memberData in pairs(gang.members) do
        local member = player.GetBySteamID(steamid)
        if IsValid(member) then
            DarkRP.notify(member, 0, 4, reason)
            UpdatePlayerGangInfo(member)
        end
    end

    print("[Çete Sistemi] '" .. gang.name .. "' çetesi silindi: " .. reason)

    -- Bellekten sil
    GangSystem.Gangs[gangID] = nil

    return true, GangSystem.Config.Messages.GangDeleted
end

-- Lider çeteyi sil
function GangSystem.LeaderDeleteGang(ply, gangID)
    if not IsValid(ply) then return false, "Geçersiz oyuncu!" end
    if not GangSystem.Gangs[gangID] then return false, GangSystem.Config.Messages.GangNotFound end

    local gang = GangSystem.Gangs[gangID]
    if gang.leader ~= ply:SteamID() then return false, GangSystem.Config.Messages.NoPermission end

    return GangSystem.DeleteGang(gangID, "Çete lideri tarafından silindi!")
end

-- Oyuncuyu çeteye davet et
function GangSystem.InvitePlayer(ply, target)
    if not IsValid(ply) or not IsValid(target) then return false, "Geçersiz oyuncu!" end
    if not GangSystem.HasGang(ply) then return false, GangSystem.Config.Messages.NotInGang end
    if GangSystem.HasGang(target) then return false, "Oyuncu zaten bir çetede!" end
    if ply.GangRank < 2 then return false, GangSystem.Config.Messages.NoPermission end
    if not GangSystem.CanJoinGang(target) then return false, "Bu oyuncunun mesleği çeteye katılamaz!" end

    local gang = GangSystem.Gangs[ply.GangID]
    if not gang then return false, GangSystem.Config.Messages.GangNotFound end

    -- Üye sayısı kontrolü
    if table.Count(gang.members) >= GangSystem.Config.MaxMembers then
        return false, "Çete dolu!"
    end

    -- Davet gönder
    GangSystem.PlayerInvites[target:SteamID()] = {
        gangID = ply.GangID,
        inviter = ply:SteamID(),
        time = CurTime()
    }

    net.Start("GangSystem.InviteReceived")
    net.WriteString(gang.name)
    net.WriteString(ply:Nick())
    net.Send(target)

    return true, GangSystem.Config.Messages.PlayerInvited
end

-- Daveti kabul et
function GangSystem.AcceptInvite(ply)
    if not IsValid(ply) then return false, "Geçersiz oyuncu!" end
    if GangSystem.HasGang(ply) then return false, GangSystem.Config.Messages.AlreadyInGang end
    if not GangSystem.CanJoinGang(ply) then return false, "Bu meslek çeteye katılamaz!" end

    local steamid = ply:SteamID()
    local invite = GangSystem.PlayerInvites[steamid]

    if not invite then return false, "Davetiniz yok!" end
    if CurTime() - invite.time > 60 then -- 60 saniye timeout
        GangSystem.PlayerInvites[steamid] = nil
        return false, "Davet süresi doldu!"
    end

    local gang = GangSystem.Gangs[invite.gangID]
    if not gang then
        GangSystem.PlayerInvites[steamid] = nil
        return false, GangSystem.Config.Messages.GangNotFound
    end

    -- Belleğe ekle (veritabanı yok)
    local time = os.time()
    gang.members[steamid] = {rank = 1, joined = time, player = ply}

    -- Daveti temizle
    GangSystem.PlayerInvites[steamid] = nil

    UpdatePlayerGangInfo(ply)

    print("[Çete Sistemi] " .. ply:Nick() .. " '" .. gang.name .. "' çetesine katıldı!")

    return true, "Çeteye katıldınız!"
end

-- Network mesajları
util.AddNetworkString("GangSystem.OpenMenu")
util.AddNetworkString("GangSystem.CreateGang")
util.AddNetworkString("GangSystem.InviteReceived")
util.AddNetworkString("GangSystem.AcceptInvite")
util.AddNetworkString("GangSystem.UpdateData")
util.AddNetworkString("GangSystem.KickPlayer")
util.AddNetworkString("GangSystem.GangMessage")

-- Menüyü aç
net.Receive("GangSystem.OpenMenu", function(len, ply)
    if not IsValid(ply) then return end

    print("[Çete Sistemi] " .. ply:Nick() .. " menü açma isteği gönderdi")

    -- Debug: Meslek bilgilerini kontrol et
    local job = ply:getDarkRPVar("job") or ply:Team()
    local jobTable = RPExtraTeams[job]
    if jobTable then
        print("[Çete Debug] " .. ply:Nick() .. " - Job ID: " .. tostring(job) .. ", Command: '" .. tostring(jobTable.command) .. "', Name: '" .. tostring(jobTable.name) .. "'")
    else
        print("[Çete Debug] " .. ply:Nick() .. " - JobTable bulunamadı! Job ID: " .. tostring(job))
    end

    -- Oyuncu verilerini gönder
    local data = {
        hasGang = GangSystem.HasGang(ply),
        canCreate = GangSystem.CanCreateGang(ply),
        gangData = ply.GangID and GangSystem.Gangs[ply.GangID] or nil,
        playerRank = ply.GangRank or 0
    }

    print("[Çete Sistemi] Oyuncu verileri: hasGang=" .. tostring(data.hasGang) .. ", canCreate=" .. tostring(data.canCreate))

    net.Start("GangSystem.UpdateData")
    net.WriteTable(data)
    net.Send(ply)

    print("[Çete Sistemi] Veri gönderildi!")
end)

-- Çete oluştur
net.Receive("GangSystem.CreateGang", function(len, ply)
    local name = net.ReadString()
    local success, msg = GangSystem.CreateGang(ply, name)

    DarkRP.notify(ply, success and 1 or 0, 4, msg)

    if success then
        -- Güncel verileri gönder
        net.Start("GangSystem.UpdateData")
        net.WriteTable({
            hasGang = true,
            canCreate = false,
            gangData = GangSystem.Gangs[ply.GangID],
            playerRank = ply.GangRank
        })
        net.Send(ply)
    end
end)

-- Daveti kabul et
net.Receive("GangSystem.AcceptInvite", function(len, ply)
    local success, msg = GangSystem.AcceptInvite(ply)
    DarkRP.notify(ply, success and 1 or 0, 4, msg)
end)

-- Oyuncu öldüğünde
hook.Add("PlayerDeath", "GangSystem.PlayerDeath", function(victim, inflictor, attacker)
    if not IsValid(victim) then return end
    if not GangSystem.HasGang(victim) then return end

    -- Lider öldüyse çeteyi kapat
    local gang = GangSystem.Gangs[victim.GangID]
    if gang and gang.leader == victim:SteamID() then
        timer.Simple(1, function() -- 1 saniye bekle
            GangSystem.DeleteGang(victim.GangID, "Çete lideri öldü, çete kapatıldı!")
        end)
    end
end)

-- Oyuncu ayrıldığında
hook.Add("PlayerDisconnected", "GangSystem.PlayerDisconnect", function(ply)
    if not IsValid(ply) then return end
    if not GangSystem.HasGang(ply) then return end

    local steamid = ply:SteamID()
    local gang = GangSystem.Gangs[ply.GangID]

    -- Lider ayrıldıysa çeteyi kapat
    if gang and gang.leader == steamid then
        timer.Simple(0.1, function() -- Kısa bir gecikme
            GangSystem.DeleteGang(ply.GangID, "Çete lideri sunucudan ayrıldı, çete kapatıldı!")
        end)
    else
        -- Normal üye ayrıldıysa sadece çeteden çıkar
        if gang and gang.members[steamid] then
            gang.members[steamid] = nil
            print("[Çete Sistemi] " .. ply:Nick() .. " çeteden ayrıldı (disconnect)")
        end
    end

    -- Davetleri temizle
    GangSystem.PlayerInvites[steamid] = nil
end)

-- Meslek değiştiğinde kontrol et
hook.Add("OnPlayerChangedTeam", "GangSystem.JobChange", function(ply, before, after)
    if not IsValid(ply) then return end
    if not GangSystem.HasGang(ply) then return end

    -- Yeni meslek çeteye uygun değilse çeteden at
    if not GangSystem.CanJoinGang(ply) then
        local gang = GangSystem.Gangs[ply.GangID]
        if gang then
            -- Lider ise çeteyi kapat
            if gang.leader == ply:SteamID() then
                GangSystem.DeleteGang(ply.GangID, "Çete lideri uygun olmayan mesleğe geçti, çete kapatıldı!")
            else
                -- Normal üye ise çeteden at
                gang.members[ply:SteamID()] = nil
                UpdatePlayerGangInfo(ply)
                DarkRP.notify(ply, 0, 4, "Meslek değişikliği nedeniyle çeteden atıldınız!")
                print("[Çete Sistemi] " .. ply:Nick() .. " meslek değişikliği nedeniyle çeteden atıldı")
            end
        end
    end
end)

-- Sunucu başladığında
hook.Add("Initialize", "GangSystem.Initialize", function()
    -- Çete sistemini başlat
    GangSystem.Gangs = {}
    GangSystem.PlayerInvites = {}
    GangSystem.NextGangID = 1

    print("[Çete Sistemi] Sunucu başlatıldı - Bellek tabanlı sistem aktif!")
    print("[Çete Sistemi] Veritabanı kullanılmıyor, veriler RAM'de saklanıyor.")
end)
