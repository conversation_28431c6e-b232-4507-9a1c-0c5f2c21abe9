-- Çete Sistemi Komutları
GangSystem = GangSystem or {}

-- Oyuncu çete bilgilerini güncelle (sv_gang_core.lua'dan kopyalandı)
local function UpdatePlayerGangInfo(ply)
    if not IsValid(ply) then return end

    local steamid = ply:SteamID()
    ply.GangID = nil
    ply.GangRank = nil

    for gangID, gang in pairs(GangSystem.Gangs) do
        if gang.members[steamid] then
            ply.GangID = gangID
            ply.GangRank = gang.members[steamid].rank
            break
        end
    end
end

-- Chat komutları
local function GangChatCommand(ply, text, teamChat)
    if not IsValid(ply) then return end
    
    local args = string.Explode(" ", text)
    local cmd = string.lower(args[1] or "")
    
    if cmd == "!cete" or cmd == "/cete" then
        -- <PERSON><PERSON><PERSON><PERSON> aç (client tarafında halledilecek)
        return ""
    elseif cmd == "!ceteolustur" or cmd == "/ceteolustur" then
        if not args[2] then
            DarkRP.notify(ply, 0, 4, "Kullanım: !ceteolustur <isim>")
            return ""
        end
        
        local name = table.concat(args, " ", 2)
        local success, msg = GangSystem.CreateGang(ply, name)
        DarkRP.notify(ply, success and 1 or 0, 4, msg)
        return ""
    elseif cmd == "!cetesil" or cmd == "/cetesil" then
        if not GangSystem.HasGang(ply) then
            DarkRP.notify(ply, 0, 4, GangSystem.Config.Messages.NotInGang)
            return ""
        end

        local success, msg = GangSystem.LeaderDeleteGang(ply, ply.GangID)
        DarkRP.notify(ply, success and 1 or 0, 4, msg)
        return ""
    elseif cmd == "!cetedavet" or cmd == "/cetedavet" then
        if not args[2] then
            DarkRP.notify(ply, 0, 4, "Kullanım: !cetedavet <oyuncu>")
            return ""
        end
        
        local target = DarkRP.findPlayer(args[2])
        if not IsValid(target) then
            DarkRP.notify(ply, 0, 4, "Oyuncu bulunamadı!")
            return ""
        end
        
        local success, msg = GangSystem.InvitePlayer(ply, target)
        DarkRP.notify(ply, success and 1 or 0, 4, msg)
        return ""
    elseif cmd == "!ceteat" or cmd == "/ceteat" then
        if not args[2] then
            DarkRP.notify(ply, 0, 4, "Kullanım: !ceteat <oyuncu>")
            return ""
        end
        
        if not GangSystem.HasGang(ply) then
            DarkRP.notify(ply, 0, 4, GangSystem.Config.Messages.NotInGang)
            return ""
        end
        
        if ply.GangRank < 2 then
            DarkRP.notify(ply, 0, 4, GangSystem.Config.Messages.NoPermission)
            return ""
        end
        
        local target = DarkRP.findPlayer(args[2])
        if not IsValid(target) then
            DarkRP.notify(ply, 0, 4, "Oyuncu bulunamadı!")
            return ""
        end
        
        local success, msg = GangSystem.KickPlayer(ply, target)
        DarkRP.notify(ply, success and 1 or 0, 4, msg)
        return ""
    elseif cmd == "!cetechat" or cmd == "/cetechat" then
        if not GangSystem.HasGang(ply) then
            DarkRP.notify(ply, 0, 4, GangSystem.Config.Messages.NotInGang)
            return ""
        end
        
        if not args[2] then
            DarkRP.notify(ply, 0, 4, "Kullanım: !cetechat <mesaj>")
            return ""
        end
        
        local message = table.concat(args, " ", 2)
        GangSystem.SendGangMessage(ply, message)
        return ""
    end
    
    return text
end

-- Oyuncuyu çeteden at
function GangSystem.KickPlayer(ply, target)
    if not IsValid(ply) or not IsValid(target) then return false, "Geçersiz oyuncu!" end
    if not GangSystem.HasGang(ply) then return false, GangSystem.Config.Messages.NotInGang end
    if not GangSystem.HasGang(target) then return false, "Oyuncu bir çetede değil!" end
    if ply.GangID ~= target.GangID then return false, "Oyuncu sizin çetenizde değil!" end
    if ply.GangRank <= target.GangRank then return false, GangSystem.Config.Messages.NoPermission end
    
    local gang = GangSystem.Gangs[ply.GangID]
    if not gang then return false, GangSystem.Config.Messages.GangNotFound end
    
    local targetSteamID = target:SteamID()

    -- Bellekten sil (veritabanı yok)
    gang.members[targetSteamID] = nil
    
    -- Oyuncu bilgilerini güncelle
    UpdatePlayerGangInfo(target)
    
    DarkRP.notify(target, 0, 4, "Çeteden atıldınız!")
    
    return true, GangSystem.Config.Messages.PlayerKicked
end

-- Çete mesajı gönder
function GangSystem.SendGangMessage(ply, message)
    if not IsValid(ply) then return end
    if not GangSystem.HasGang(ply) then return end
    
    local gang = GangSystem.Gangs[ply.GangID]
    if not gang then return end
    
    local rank = GangSystem.Config.Ranks[ply.GangRank]
    local rankName = rank and rank.name or "Üye"
    local rankColor = rank and rank.color or Color(255, 255, 255)
    
    -- Çete üyelerine mesaj gönder
    for steamid, _ in pairs(gang.members) do
        local member = player.GetBySteamID(steamid)
        if IsValid(member) then
            chat.AddText(member, 
                Color(100, 255, 100), "[ÇETE] ",
                rankColor, "[" .. rankName .. "] ",
                Color(255, 255, 255), ply:Nick() .. ": ",
                Color(200, 200, 200), message
            )
        end
    end
end

-- Admin komutları
local function AdminGangCommand(ply, cmd, args)
    if not ply:IsSuperAdmin() then
        DarkRP.notify(ply, 0, 4, "Bu komutu kullanma yetkiniz yok!")
        return
    end
    
    if cmd == "gang_list" then
        ply:ChatPrint("=== Çete Listesi ===")
        for id, gang in pairs(GangSystem.Gangs) do
            ply:ChatPrint(string.format("%d. %s - Üye: %d - Lider: %s", 
                id, gang.name, table.Count(gang.members), gang.leader))
        end
    elseif cmd == "gang_delete" then
        if not args[1] then
            ply:ChatPrint("Kullanım: gang_delete <çete_id>")
            return
        end
        
        local gangID = tonumber(args[1])
        if not GangSystem.Gangs[gangID] then
            ply:ChatPrint("Çete bulunamadı!")
            return
        end
        
        -- Çeteyi sil (veritabanı yok)
        GangSystem.DeleteGang(gangID, "Çete bir admin tarafından silindi!")
        ply:ChatPrint("Çete silindi!")
    elseif cmd == "gang_info" then
        if not args[1] then
            ply:ChatPrint("Kullanım: gang_info <çete_id>")
            return
        end
        
        local gangID = tonumber(args[1])
        local gang = GangSystem.Gangs[gangID]
        if not gang then
            ply:ChatPrint("Çete bulunamadı!")
            return
        end
        
        ply:ChatPrint("=== " .. gang.name .. " ===")
        ply:ChatPrint("ID: " .. gang.id)
        ply:ChatPrint("Lider: " .. gang.leader)
        ply:ChatPrint("Oluşturulma: " .. os.date("%d/%m/%Y %H:%M", gang.created))
        ply:ChatPrint("Para: " .. DarkRP.formatMoney(gang.money))
        ply:ChatPrint("Üyeler:")
        
        for steamid, memberData in pairs(gang.members) do
            local member = player.GetBySteamID(steamid)
            local name = IsValid(member) and member:Nick() or "Çevrimdışı"
            local rank = GangSystem.Config.Ranks[memberData.rank]
            ply:ChatPrint("  - " .. name .. " (" .. steamid .. ") - " .. (rank and rank.name or "Bilinmiyor"))
        end
    end
end

-- Hook'ları ekle
hook.Add("PlayerSay", "GangSystem.ChatCommands", GangChatCommand)

-- Console komutları
concommand.Add("gang_list", AdminGangCommand)
concommand.Add("gang_delete", AdminGangCommand)
concommand.Add("gang_info", AdminGangCommand)

-- Dosyaları yeniden yükle komutu
concommand.Add("gang_reload", function(ply, cmd, args)
    if IsValid(ply) and not ply:IsSuperAdmin() then
        ply:ChatPrint("Bu komutu sadece süper adminler kullanabilir!")
        return
    end

    print("[Çete Sistemi] Dosyalar yeniden yükleniyor...")

    -- Shared dosyaları yeniden yükle
    include("gang_system/shared/sh_gang_config.lua")
    include("gang_system/shared/sh_gang_shared.lua")

    -- Client'lara da gönder
    for _, p in ipairs(player.GetAll()) do
        if IsValid(p) then
            p:SendLua('include("gang_system/shared/sh_gang_config.lua")')
            p:SendLua('include("gang_system/shared/sh_gang_shared.lua")')
        end
    end

    print("[Çete Sistemi] Dosyalar yeniden yüklendi!")
    if IsValid(ply) then
        ply:ChatPrint("[Çete Sistemi] Dosyalar yeniden yüklendi!")
    end
end)

-- Test komutu - belirli oyuncunun çete oluşturma yetkisini kontrol et
concommand.Add("gang_test_player", function(ply, cmd, args)
    if IsValid(ply) and not ply:IsSuperAdmin() then
        ply:ChatPrint("Bu komutu sadece süper adminler kullanabilir!")
        return
    end

    local targetName = args[1]
    if not targetName then
        print("Kullanım: gang_test_player <oyuncu_adı>")
        return
    end

    local target = nil
    for _, p in ipairs(player.GetAll()) do
        if string.find(string.lower(p:Nick()), string.lower(targetName)) then
            target = p
            break
        end
    end

    if not target then
        print("Oyuncu bulunamadı: " .. targetName)
        return
    end

    print("=== " .. target:Nick() .. " Test Sonuçları ===")
    local job = target:getDarkRPVar("job") or target:Team()
    print("Job ID: " .. tostring(job))
    print("Job Type: " .. type(job))

    local jobTable = RPExtraTeams[job]
    if jobTable then
        print("JobTable bulundu!")
        print("Command: " .. tostring(jobTable.command))
        print("Name: " .. tostring(jobTable.name))
    else
        print("JobTable bulunamadı - Özel job (Mayor gibi)")
    end

    local canCreate = GangSystem.CanCreateGang(target)
    print("Çete oluşturabilir: " .. tostring(canCreate))

    -- Config kontrolü
    local jobStr = string.lower(tostring(job))
    print("Config'de '" .. jobStr .. "' var mı: " .. tostring(GangSystem.Config.AllowedCreateJobs[jobStr] or false))
    print("Config'de '" .. tostring(job) .. "' var mı: " .. tostring(GangSystem.Config.AllowedCreateJobs[tostring(job)] or false))
end)
