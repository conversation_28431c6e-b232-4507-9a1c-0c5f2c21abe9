-- Modern Çete UI
GangSystem = GangSystem or {}
GangSystem.UI = {}

local PANEL = {}

function PANEL:Init()
    print("[Çete Sistemi] Panel Init çalışıyor...")

    self:SetSize(800, 600)
    self:Center()
    self:SetTitle("")
    self:SetDraggable(true)
    self:ShowCloseButton(false)
    self:MakePopup()

    self.playerData = {}
    self.animProgress = 0
    self.hoverButton = nil

    -- Animasyon
    self:AlphaTo(255, 0.3, 0)

    -- Veri al
    print("[Çete Sistemi] Network mesajı gönderiliyor...")
    if net and net.Start then
        net.Start("GangSystem.OpenMenu")
        net.SendToServer()
    else
        print("[Çete Sistemi] UYARI: Network kullanılamıyor, varsayılan veri kullanılıyor")
        -- Varsayılan veri
        timer.Simple(0.1, function()
            if IsValid(self) then
                self.playerData = {
                    hasGang = false,
                    canCreate = true,
                    gangData = nil,
                    playerRank = 0
                }
                print("[Çete Sistemi] Varsayılan veri yüklendi!")
            end
        end)
    end

    print("[Çete Sistemi] Panel başarıyla oluşturuldu!")
end

function PANEL:Paint(w, h)
    -- Arka plan blur efekti
    Derma_DrawBackgroundBlur(self, self.m_fCreateTime)
    
    -- Ana arka plan
    surface.SetDrawColor(GangSystem.Config.Colors.Primary)
    surface.DrawRect(0, 0, w, h)
    
    -- Üst bar
    surface.SetDrawColor(GangSystem.Config.Colors.Secondary)
    surface.DrawRect(0, 0, w, 60)
    
    -- Başlık
    surface.SetFont("GangSystem.Title")
    surface.SetTextColor(GangSystem.Config.Colors.Text)
    surface.SetTextPos(20, 20)
    surface.DrawText("Çete Sistemi")
    
    -- Kapatma butonu
    local closeX, closeY = w - 40, 20
    local mx, my = self:ScreenToLocal(gui.MouseX(), gui.MouseY())
    local closeHover = mx >= closeX and mx <= closeX + 20 and my >= closeY and my <= closeY + 20
    
    surface.SetDrawColor(closeHover and GangSystem.Config.Colors.Danger or Color(100, 100, 100))
    surface.DrawRect(closeX, closeY, 20, 20)
    
    surface.SetFont("GangSystem.Text")
    surface.SetTextColor(255, 255, 255)
    surface.SetTextPos(closeX + 6, closeY + 2)
    surface.DrawText("×")
    
    -- İçerik alanı
    self:PaintContent(20, 80, w - 40, h - 100)
end

function PANEL:PaintContent(x, y, w, h)
    if not self.playerData.hasGang then
        if self.playerData.canCreate then
            self:PaintCreateGang(x, y, w, h)
        else
            self:PaintGangList(x, y, w, h)
        end
    else
        self:PaintGangManagement(x, y, w, h)
    end
end

function PANEL:PaintCreateGang(x, y, w, h)
    -- Çete oluşturma paneli
    local panelW, panelH = 400, 300
    local panelX, panelY = x + w/2 - panelW/2, y + h/2 - panelH/2
    
    -- Panel arka planı
    GangSystem.DrawShadow(panelX, panelY, panelW, panelH, 5)
    surface.SetDrawColor(GangSystem.Config.Colors.Secondary)
    surface.DrawRect(panelX, panelY, panelW, panelH)
    
    -- Başlık
    surface.SetFont("GangSystem.Title")
    local title = "Çete Oluştur"
    local tw, th = surface.GetTextSize(title)
    surface.SetTextColor(GangSystem.Config.Colors.Text)
    surface.SetTextPos(panelX + panelW/2 - tw/2, panelY + 20)
    surface.DrawText(title)
    
    if not self.playerData.canCreate then
        -- Yetki yok mesajı
        surface.SetFont("GangSystem.Text")
        local msg = "Bu meslek çete oluşturamaz!"
        local mw, mh = surface.GetTextSize(msg)
        surface.SetTextColor(GangSystem.Config.Colors.Danger)
        surface.SetTextPos(panelX + panelW/2 - mw/2, panelY + 100)
        surface.DrawText(msg)
        return
    end
    
    -- Maliyet bilgisi
    surface.SetFont("GangSystem.Text")
    local cost = "Maliyet: " .. DarkRP.formatMoney(GangSystem.Config.CreateCost)
    local cw, ch = surface.GetTextSize(cost)
    surface.SetTextColor(GangSystem.Config.Colors.TextSecondary)
    surface.SetTextPos(panelX + panelW/2 - cw/2, panelY + 70)
    surface.DrawText(cost)
    
    -- İsim girişi (TextEntry burada olacak)
    if not self.nameEntry then
        self.nameEntry = vgui.Create("DTextEntry", self)
        self.nameEntry:SetPos(panelX + 50, panelY + 120)
        self.nameEntry:SetSize(panelW - 100, 30)
        self.nameEntry:SetPlaceholderText("Çete İsmi...")
        self.nameEntry:SetFont("GangSystem.Text")
    end
    
    -- Oluştur butonu
    local btnX, btnY = panelX + 50, panelY + 200
    local btnW, btnH = panelW - 100, 40
    local mx, my = self:ScreenToLocal(gui.MouseX(), gui.MouseY())
    local btnHover = mx >= btnX and mx <= btnX + btnW and my >= btnY and my <= btnY + btnH
    
    GangSystem.DrawButton(btnX, btnY, btnW, btnH, "Çete Oluştur", GangSystem.Config.Colors.Success, btnHover)
    
    if btnHover and input.IsMouseDown(MOUSE_LEFT) and not self.clicking then
        self.clicking = true
        self:CreateGang()
    elseif not input.IsMouseDown(MOUSE_LEFT) then
        self.clicking = false
    end
end

function PANEL:PaintGangManagement(x, y, w, h)
    local gang = self.playerData.gangData
    if not gang then return end
    
    -- Çete bilgileri paneli
    local infoW, infoH = w, 100
    
    GangSystem.DrawShadow(x, y, infoW, infoH, 3)
    surface.SetDrawColor(GangSystem.Config.Colors.Secondary)
    surface.DrawRect(x, y, infoW, infoH)
    
    -- Çete ismi
    surface.SetFont("GangSystem.Title")
    surface.SetTextColor(GangSystem.Config.Colors.Text)
    surface.SetTextPos(x + 20, y + 20)
    surface.DrawText(gang.name)
    
    -- Üye sayısı
    surface.SetFont("GangSystem.Text")
    local memberCount = "Üyeler: " .. table.Count(gang.members) .. "/" .. GangSystem.Config.MaxMembers
    surface.SetTextColor(GangSystem.Config.Colors.TextSecondary)
    surface.SetTextPos(x + 20, y + 50)
    surface.DrawText(memberCount)
    
    -- Rütbe
    local rankInfo = GangSystem.Config.Ranks[self.playerData.playerRank]
    if rankInfo then
        surface.SetTextColor(rankInfo.color)
        surface.SetTextPos(x + 200, y + 50)
        surface.DrawText("Rütbe: " .. rankInfo.name)
    end
    
    -- Çete yönetim butonları (sadece yetkili üyeler için)
    if self.playerData.playerRank >= 2 then
        self:PaintManagementButtons(x, y + 120, w, 60)
        self:PaintMemberList(x, y + 190, w, h - 190)
    else
        self:PaintMemberList(x, y + 120, w, h - 120)
    end
end

-- Çete yönetim butonları
function PANEL:PaintManagementButtons(x, y, w, h)
    local btnW, btnH = 120, 35
    local spacing = 10
    local startX = x + 10

    -- Oyuncu davet et butonu
    local inviteBtn = {x = startX, y = y + 10, w = btnW, h = btnH}
    local mx, my = self:ScreenToLocal(gui.MouseX(), gui.MouseY())
    local inviteHover = mx >= inviteBtn.x and mx <= inviteBtn.x + inviteBtn.w and my >= inviteBtn.y and my <= inviteBtn.y + inviteBtn.h

    GangSystem.DrawButton(inviteBtn.x, inviteBtn.y, inviteBtn.w, inviteBtn.h, "Davet Et", GangSystem.Config.Colors.Success, inviteHover)

    -- Çete sil butonu (sadece lider için)
    if self.playerData.playerRank >= 3 then
        local deleteBtn = {x = startX + btnW + spacing, y = y + 10, w = btnW, h = btnH}
        local deleteHover = mx >= deleteBtn.x and mx <= deleteBtn.x + deleteBtn.w and my >= deleteBtn.y and my <= deleteBtn.y + deleteBtn.h

        GangSystem.DrawButton(deleteBtn.x, deleteBtn.y, deleteBtn.w, deleteBtn.h, "Çeteyi Sil", GangSystem.Config.Colors.Danger, deleteHover)

        -- Buton tıklama kontrolü
        if deleteHover and input.IsMouseDown(MOUSE_LEFT) and not self.deleteClicking then
            self.deleteClicking = true
            self:ShowDeleteConfirm()
        elseif not input.IsMouseDown(MOUSE_LEFT) then
            self.deleteClicking = false
        end
    end

    -- Davet butonu tıklama kontrolü
    if inviteHover and input.IsMouseDown(MOUSE_LEFT) and not self.inviteClicking then
        self.inviteClicking = true
        self:ShowInviteMenu()
    elseif not input.IsMouseDown(MOUSE_LEFT) then
        self.inviteClicking = false
    end
end

-- Çete listesi (çete oluşturamayan meslekler için)
function PANEL:PaintGangList(x, y, w, h)
    -- Başlık
    surface.SetFont("GangSystem.Title")
    local title = "Mevcut Çeteler"
    local tw, th = surface.GetTextSize(title)
    surface.SetTextColor(GangSystem.Config.Colors.Text)
    surface.SetTextPos(x + w/2 - tw/2, y + 20)
    surface.DrawText(title)

    -- Açıklama
    surface.SetFont("GangSystem.Text")
    local desc = "Bu meslek çete oluşturamaz, sadece mevcut çeteleri görüntüleyebilirsiniz."
    local dw, dh = surface.GetTextSize(desc)
    surface.SetTextColor(GangSystem.Config.Colors.TextSecondary)
    surface.SetTextPos(x + w/2 - dw/2, y + 60)
    surface.DrawText(desc)

    -- Çete listesi
    local listY = y + 100
    local itemH = 50
    local itemSpacing = 5

    if self.playerData.gangList and #self.playerData.gangList > 0 then
        for i, gang in ipairs(self.playerData.gangList) do
            local itemY = listY + (i - 1) * (itemH + itemSpacing)

            -- Çete item arka planı
            surface.SetDrawColor(GangSystem.Config.Colors.Secondary)
            surface.DrawRect(x + 10, itemY, w - 20, itemH)

            -- Çete ismi
            surface.SetFont("GangSystem.Button")
            surface.SetTextColor(GangSystem.Config.Colors.Text)
            surface.SetTextPos(x + 20, itemY + 10)
            surface.DrawText(gang.name)

            -- Üye sayısı
            surface.SetFont("GangSystem.Text")
            local memberText = "Üyeler: " .. gang.memberCount .. "/" .. GangSystem.Config.MaxMembers
            surface.SetTextColor(GangSystem.Config.Colors.TextSecondary)
            surface.SetTextPos(x + 20, itemY + 30)
            surface.DrawText(memberText)

            -- Lider
            surface.SetTextColor(GangSystem.Config.Colors.Accent)
            surface.SetTextPos(x + w - 200, itemY + 15)
            surface.DrawText("Lider: " .. (gang.leaderName or "Bilinmiyor"))
        end
    else
        -- Çete yok mesajı
        surface.SetFont("GangSystem.Text")
        local noGangText = "Henüz hiç çete oluşturulmamış."
        local ngw, ngh = surface.GetTextSize(noGangText)
        surface.SetTextColor(GangSystem.Config.Colors.TextSecondary)
        surface.SetTextPos(x + w/2 - ngw/2, listY + 50)
        surface.DrawText(noGangText)
    end

-- Davet menüsü göster
function PANEL:ShowInviteMenu()
    local inviteFrame = vgui.Create("DFrame")
    inviteFrame:SetSize(300, 200)
    inviteFrame:Center()
    inviteFrame:SetTitle("Oyuncu Davet Et")
    inviteFrame:MakePopup()

    local playerList = vgui.Create("DComboBox", inviteFrame)
    playerList:SetPos(20, 50)
    playerList:SetSize(260, 25)
    playerList:SetValue("Oyuncu Seçin...")

    -- Online oyuncuları ekle
    for _, ply in ipairs(player.GetAll()) do
        if IsValid(ply) and ply ~= LocalPlayer() then
            playerList:AddChoice(ply:Nick(), ply)
        end
    end

    local inviteBtn = vgui.Create("DButton", inviteFrame)
    inviteBtn:SetPos(20, 100)
    inviteBtn:SetSize(100, 30)
    inviteBtn:SetText("Davet Et")
    inviteBtn.DoClick = function()
        local _, selectedPlayer = playerList:GetSelected()
        if IsValid(selectedPlayer) then
            net.Start("GangSystem.InvitePlayer")
            net.WriteEntity(selectedPlayer)
            net.SendToServer()
            inviteFrame:Close()
        end
    end

    local cancelBtn = vgui.Create("DButton", inviteFrame)
    cancelBtn:SetPos(140, 100)
    cancelBtn:SetSize(100, 30)
    cancelBtn:SetText("İptal")
    cancelBtn.DoClick = function()
        inviteFrame:Close()
    end
end

-- Çete silme onayı
function PANEL:ShowDeleteConfirm()
    local confirmFrame = vgui.Create("DFrame")
    confirmFrame:SetSize(300, 150)
    confirmFrame:Center()
    confirmFrame:SetTitle("Çete Sil")
    confirmFrame:MakePopup()

    local label = vgui.Create("DLabel", confirmFrame)
    label:SetPos(20, 50)
    label:SetSize(260, 20)
    label:SetText("Çeteyi silmek istediğinizden emin misiniz?")

    local yesBtn = vgui.Create("DButton", confirmFrame)
    yesBtn:SetPos(20, 90)
    yesBtn:SetSize(80, 30)
    yesBtn:SetText("Evet")
    yesBtn.DoClick = function()
        net.Start("GangSystem.DeleteGang")
        net.SendToServer()
        confirmFrame:Close()
        self:Close()
    end

    local noBtn = vgui.Create("DButton", confirmFrame)
    noBtn:SetPos(120, 90)
    noBtn:SetSize(80, 30)
    noBtn:SetText("Hayır")
    noBtn.DoClick = function()
        confirmFrame:Close()
    end
end

function PANEL:PaintMemberList(x, y, w, h)
    local gang = self.playerData.gangData
    if not gang then return end
    
    surface.SetFont("GangSystem.Text")
    surface.SetTextColor(GangSystem.Config.Colors.Text)
    surface.SetTextPos(x, y)
    surface.DrawText("Çete Üyeleri:")
    
    local memberY = y + 30
    local memberH = 40
    
    for steamid, memberData in pairs(gang.members) do
        local ply = player.GetBySteamID(steamid)
        local name = IsValid(ply) and ply:Nick() or "Çevrimdışı"
        local rank = GangSystem.Config.Ranks[memberData.rank]
        
        -- Üye arka planı
        surface.SetDrawColor(GangSystem.Config.Colors.Secondary)
        surface.DrawRect(x, memberY, w, memberH)
        
        -- İsim
        surface.SetFont("GangSystem.Text")
        surface.SetTextColor(GangSystem.Config.Colors.Text)
        surface.SetTextPos(x + 10, memberY + 10)
        surface.DrawText(name)
        
        -- Rütbe
        if rank then
            surface.SetTextColor(rank.color)
            surface.SetTextPos(x + 200, memberY + 10)
            surface.DrawText(rank.name)
        end
        
        -- Online durumu
        local status = IsValid(ply) and "Çevrimiçi" or "Çevrimdışı"
        local statusColor = IsValid(ply) and GangSystem.Config.Colors.Success or GangSystem.Config.Colors.TextSecondary
        surface.SetTextColor(statusColor)
        surface.SetTextPos(x + 300, memberY + 10)
        surface.DrawText(status)
        
        memberY = memberY + memberH + 5
    end
end

function PANEL:CreateGang()
    if not self.nameEntry then return end
    
    local name = self.nameEntry:GetValue()
    if not GangSystem.IsValidGangName(name) then
        chat.AddText(Color(255, 0, 0), "[Çete] Geçersiz çete ismi!")
        return
    end
    
    net.Start("GangSystem.CreateGang")
    net.WriteString(name)
    net.SendToServer()
end

function PANEL:OnMousePressed(code)
    if code == MOUSE_LEFT then
        local x, y = self:GetPos()
        local w, h = self:GetSize()
        local mx, my = gui.MouseX() - x, gui.MouseY() - y
        
        -- Kapatma butonu kontrolü
        if mx >= w - 40 and mx <= w - 20 and my >= 20 and my <= 40 then
            self:Close()
        end
    end
end

vgui.Register("GangSystemMenu", PANEL, "DFrame")

-- Network mesajları
net.Receive("GangSystem.UpdateData", function()
    print("[Çete Sistemi] UpdateData mesajı alındı!")
    local data = net.ReadTable()

    print("[Çete Sistemi] Alınan veri: hasGang=" .. tostring(data.hasGang) .. ", canCreate=" .. tostring(data.canCreate))

    if IsValid(GangSystem.UI.Menu) then
        GangSystem.UI.Menu.playerData = data
        print("[Çete Sistemi] Menu verisi güncellendi!")

        -- TextEntry'yi yeniden konumlandır
        if GangSystem.UI.Menu.nameEntry and not data.hasGang then
            GangSystem.UI.Menu.nameEntry:Remove()
            GangSystem.UI.Menu.nameEntry = nil
        end
    else
        print("[Çete Sistemi] HATA: Menu geçerli değil!")
    end
end)

net.Receive("GangSystem.InviteReceived", function()
    local gangName = net.ReadString()
    local inviterName = net.ReadString()

    -- Davet popup'ı
    local popup = vgui.Create("DFrame")
    popup:SetSize(400, 200)
    popup:Center()
    popup:SetTitle("")
    popup:SetDraggable(false)
    popup:ShowCloseButton(false)
    popup:MakePopup()

    popup.Paint = function(self, w, h)
        -- Arka plan
        surface.SetDrawColor(GangSystem.Config.Colors.Primary)
        surface.DrawRect(0, 0, w, h)

        -- Başlık
        surface.SetFont("GangSystem.Title")
        local title = "Çete Daveti"
        local tw, th = surface.GetTextSize(title)
        surface.SetTextColor(GangSystem.Config.Colors.Text)
        surface.SetTextPos(w/2 - tw/2, 20)
        surface.DrawText(title)

        -- Mesaj
        surface.SetFont("GangSystem.Text")
        local msg = inviterName .. " sizi " .. gangName .. " çetesine davet etti!"
        local mw, mh = surface.GetTextSize(msg)
        surface.SetTextColor(GangSystem.Config.Colors.TextSecondary)
        surface.SetTextPos(w/2 - mw/2, 70)
        surface.DrawText(msg)
    end

    -- Kabul et butonu
    local acceptBtn = vgui.Create("DButton", popup)
    acceptBtn:SetPos(50, 130)
    acceptBtn:SetSize(120, 40)
    acceptBtn:SetText("Kabul Et")
    acceptBtn:SetFont("GangSystem.Button")
    acceptBtn.Paint = function(self, w, h)
        local color = self:IsHovered() and GangSystem.LerpColor(0.2, GangSystem.Config.Colors.Success, Color(255, 255, 255)) or GangSystem.Config.Colors.Success
        GangSystem.DrawButton(0, 0, w, h, "Kabul Et", color, self:IsHovered())
    end
    acceptBtn.DoClick = function()
        net.Start("GangSystem.AcceptInvite")
        net.SendToServer()
        popup:Close()
    end

    -- Reddet butonu
    local declineBtn = vgui.Create("DButton", popup)
    declineBtn:SetPos(230, 130)
    declineBtn:SetSize(120, 40)
    declineBtn:SetText("Reddet")
    declineBtn:SetFont("GangSystem.Button")
    declineBtn.Paint = function(self, w, h)
        local color = self:IsHovered() and GangSystem.LerpColor(0.2, GangSystem.Config.Colors.Danger, Color(255, 255, 255)) or GangSystem.Config.Colors.Danger
        GangSystem.DrawButton(0, 0, w, h, "Reddet", color, self:IsHovered())
    end
    declineBtn.DoClick = function()
        popup:Close()
    end

    -- 30 saniye sonra otomatik kapat
    timer.Simple(30, function()
        if IsValid(popup) then
            popup:Close()
        end
    end)
end)

-- Menüyü aç
function GangSystem.OpenMenu()
    print("[Çete Sistemi] Menü açılıyor...")

    if IsValid(GangSystem.UI.Menu) then
        GangSystem.UI.Menu:Close()
    end

    GangSystem.UI.Menu = vgui.Create("GangSystemMenu")
    print("[Çete Sistemi] Menü oluşturuldu!")
end

-- Chat komutu
hook.Add("OnPlayerChat", "GangSystem.ChatCommand", function(ply, text)
    if ply == LocalPlayer() then
        local cmd = string.lower(string.Trim(text))
        if cmd == "!cete" or cmd == "/cete" or cmd == "!gang" then
            print("[Çete Sistemi] Chat komutu algılandı: " .. text)
            GangSystem.OpenMenu()
            return true
        end
    end
end)

-- Console komutları
concommand.Add("gang_menu", function()
    print("[Çete Sistemi] Console komutu ile menü açılıyor...")
    GangSystem.OpenMenu()
end)

concommand.Add("gang_test_ui", function()
    print("[Çete Sistemi] UI Test başlatılıyor...")

    -- Basit test UI
    local frame = vgui.Create("DFrame")
    frame:SetSize(400, 300)
    frame:Center()
    frame:SetTitle("Çete Sistemi Test")
    frame:MakePopup()

    local label = vgui.Create("DLabel", frame)
    label:SetPos(20, 50)
    label:SetSize(360, 20)
    label:SetText("Çete Sistemi Test UI - Network olmadan")

    local btn1 = vgui.Create("DButton", frame)
    btn1:SetPos(20, 100)
    btn1:SetSize(150, 30)
    btn1:SetText("Normal Menü Aç")
    btn1.DoClick = function()
        frame:Close()
        GangSystem.OpenMenu()
    end

    local btn2 = vgui.Create("DButton", frame)
    btn2:SetPos(200, 100)
    btn2:SetSize(150, 30)
    btn2:SetText("Test Verisi ile Aç")
    btn2.DoClick = function()
        frame:Close()

        if IsValid(GangSystem.UI.Menu) then
            GangSystem.UI.Menu:Close()
        end

        GangSystem.UI.Menu = vgui.Create("GangSystemMenu")

        -- Test verisi ekle
        timer.Simple(0.1, function()
            if IsValid(GangSystem.UI.Menu) then
                GangSystem.UI.Menu.playerData = {
                    hasGang = false,
                    canCreate = true,
                    gangData = nil,
                    playerRank = 0
                }
                print("[Çete Sistemi] Test verisi eklendi!")
            end
        end)
    end

    print("[Çete Sistemi] Test UI oluşturuldu!")
end)

-- F4 menüsüne ekle (DarkRP) - Güvenli versiyon
hook.Add("F4MenuTabs", "GangSystem.F4Tab", function(tabs)
    if not tabs then
        print("[Çete Sistemi] F4 tabs parametresi nil, hook atlanıyor")
        return
    end

    tabs["Çete"] = function(parent)
        local panel = vgui.Create("DPanel", parent)
        panel:Dock(FILL)
        panel.Paint = function(self, w, h)
            surface.SetDrawColor(GangSystem.Config.Colors.Primary)
            surface.DrawRect(0, 0, w, h)
        end

        local btn = vgui.Create("DButton", panel)
        btn:SetPos(50, 50)
        btn:SetSize(200, 50)
        btn:SetText("Çete Menüsü")
        btn:SetFont("GangSystem.Button")
        btn.Paint = function(self, w, h)
            GangSystem.DrawButton(0, 0, w, h, "Çete Menüsü", GangSystem.Config.Colors.Accent, self:IsHovered())
        end
        btn.DoClick = function()
            GangSystem.OpenMenu()
        end

        return panel
    end
end)

-- Alternatif F4 menü ekleme yöntemi
hook.Add("InitPostEntity", "GangSystem.F4MenuAlternative", function()
    timer.Simple(2, function()
        -- DarkRP F4 menüsüne kategori ekleme (eğer mevcut fonksiyon varsa)
        if DarkRP and DarkRP.addF4MenuTab then
            DarkRP.addF4MenuTab("Çete", function(parent)
                local panel = vgui.Create("DPanel", parent)
                panel:Dock(FILL)

                local btn = vgui.Create("DButton", panel)
                btn:SetPos(50, 50)
                btn:SetSize(200, 50)
                btn:SetText("Çete Menüsü Aç")
                btn.DoClick = function()
                    GangSystem.OpenMenu()
                end

                return panel
            end)
            print("[Çete Sistemi] F4 menüsüne alternatif yöntemle eklendi")
        end
    end)
end)
