# Modern Çete Sistemi - Garry's Mod DarkRP

Bu addon, Garry's Mod DarkRP sunucuları için modern ve kullanıcı dostu bir çete sistemi sağlar.

## Özellikler

### 🎯 Meslek Tabanlı Sistem
- Sadece belirli meslekler çete oluşturabilir
- Farklı meslekler çeteye katılabilir
- Yapılandırılabilir meslek listeleri
- Güvenli yetki kontrolü

### 🎨 Modern UI
- Onyx/Xenin tarzında modern arayüz
- Smooth animasyonlar ve geçişler
- Responsive tasarım
- Blur efektleri ve gölgeler

### 👥 Çete Yönetimi
- Çete oluşturma ve silme
- Üye davet sistemi
- Rütbe sistemi (Üye, Yardımcı, Lider)
- Çete içi chat sistemi
- Üye atma yetkisi
- **Lider ölürse çete kapanır**
- **Lider sunucudan ayrılırsa çete kapanır**
- **Meslek değişirse çeteden atılır**

### 🔒 Güvenlik
- Sadece çete üyeleri giriş yapabilir
- Yetki tabanlı işlemler
- SQL injection koruması
- Veri doğrulama

### 💾 Bellek Tabanlı Sistem
- Veritabanı kullanılmaz (sadece RAM'de saklanır)
- Sunucu yeniden başlatıldığında çeteler sıfırlanır
- Daha hızlı performans

## Kurulum

1. Bu klasörü `garrysmod/addons/` dizinine kopyalayın
2. Sunucuyu yeniden başlatın
3. Yapılandırma dosyasını düzenleyin (isteğe bağlı)

## Yapılandırma

`lua/gang_system/shared/sh_gang_config.lua` dosyasından aşağıdaki ayarları değiştirebilirsiniz:

### Çete Oluşturabilen Meslekler
```lua
GangSystem.Config.AllowedCreateJobs = {
    ["gang"] = true,
    ["gangster"] = true,
    ["thief"] = true,
    ["drug_dealer"] = true,
    ["hitman"] = true,
    ["mob"] = true,
    ["mafia"] = true,
}
```

### Çeteye Katılabilen Meslekler
```lua
GangSystem.Config.AllowedJoinJobs = {
    ["gang"] = true,
    ["gangster"] = true,
    ["thief"] = true,
    ["drug_dealer"] = true,
    ["hitman"] = true,
    ["mob"] = true,
    ["mafia"] = true,
    ["gun_dealer"] = true,
    ["black_market_dealer"] = true,
    ["kidnapper"] = true,
}
```

### Genel Ayarlar
- `MaxGangs`: Maksimum çete sayısı (varsayılan: 10)
- `MaxMembers`: Çete başına maksimum üye (varsayılan: 8)
- `CreateCost`: Çete oluşturma maliyeti (varsayılan: 50000)
- `MinNameLength`: Minimum çete ismi uzunluğu (varsayılan: 3)
- `MaxNameLength`: Maksimum çete ismi uzunluğu (varsayılan: 20)

## Komutlar

### Oyuncu Komutları
- `!cete` / `/cete` - Çete menüsünü açar
- `!ceteolustur <isim>` - Çete oluşturur
- `!cetesil` - Çeteyi siler (sadece lider)
- `!cetedavet <oyuncu>` - Oyuncuyu çeteye davet eder
- `!ceteat <oyuncu>` - Oyuncuyu çeteden atar
- `!cetechat <mesaj>` - Çete içi mesaj gönderir

### Admin Komutları (Console)
- `gang_list` - Tüm çeteleri listeler
- `gang_delete <çete_id>` - Çeteyi siler
- `gang_info <çete_id>` - Çete detaylarını gösterir

## Rütbe Sistemi

1. **Üye** (Rank 1) - Temel üye
2. **Yardımcı** (Rank 2) - Davet etme ve atma yetkisi
3. **Lider** (Rank 3) - Tam yetki

## UI Kullanımı

### Çete Oluşturma
1. `!cete` komutu ile menüyü açın
2. Çete ismini girin
3. "Çete Oluştur" butonuna tıklayın

### Çete Yönetimi
- Çete menüsünden üyeleri görüntüleyebilirsiniz
- Online/offline durumlarını takip edebilirsiniz
- Rütbeleri ve yetkilerini görebilirsiniz

### Davet Sistemi
- Davet aldığınızda otomatik popup açılır
- 30 saniye içinde kabul/reddet seçeneği
- Sadece yetkili üyeler davet gönderebilir

## Teknik Detaylar

### Dosya Yapısı
```
lua/
├── autorun/
│   ├── server/sv_gang_init.lua
│   └── client/cl_gang_init.lua
└── gang_system/
    ├── shared/
    │   ├── sh_gang_config.lua
    │   └── sh_gang_shared.lua
    ├── server/
    │   ├── sv_gang_core.lua
    │   └── sv_gang_commands.lua
    └── client/
        └── cl_gang_ui.lua
```

### Bellek Yapısı
- `GangSystem.Gangs` - Çete bilgileri (RAM'de)
- `GangSystem.PlayerInvites` - Aktif davetler
- `GangSystem.NextGangID` - Sonraki çete ID'si

### Önemli Notlar
- **Sunucu yeniden başlatıldığında tüm çeteler silinir**
- **Lider ölürse veya ayrılırsa çete otomatik kapanır**
- **Meslek değişikliği çeteden atılmaya neden olabilir**

## Uyumluluk

- Garry's Mod 13+
- DarkRP 2.7.0+
- MySQL/SQLite desteği

## Destek

Herhangi bir sorun yaşarsanız:
1. Console'da hata mesajlarını kontrol edin
2. Yapılandırma dosyasını gözden geçirin
3. Sunucu loglarını inceleyin

## Lisans

Bu addon açık kaynak kodludur ve özgürce kullanılabilir.
