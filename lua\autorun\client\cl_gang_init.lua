-- İstemci Tarafı Başlatma
if CLIENT then
    -- Paylaşılan dosyaları yükle
    include("gang_system/shared/sh_gang_config.lua")
    include("gang_system/shared/sh_gang_shared.lua")
    include("gang_system/client/cl_gang_ui.lua")
    
    -- Fontları oluştur
    surface.CreateFont("GangSystem.Title", {
        font = "Roboto",
        size = 24,
        weight = 600,
        antialias = true,
    })
    
    surface.CreateFont("GangSystem.Button", {
        font = "Roboto",
        size = 16,
        weight = 500,
        antialias = true,
    })
    
    surface.CreateFont("GangSystem.Text", {
        font = "Roboto",
        size = 14,
        weight = 400,
        antialias = true,
    })
    
    surface.CreateFont("GangSystem.Small", {
        font = "Roboto",
        size = 12,
        weight = 400,
        antialias = true,
    })
    
    print("[Çete Sistemi] İstemci tarafı yüklendi!")

    -- Test komutu
    timer.Simple(2, function()
        print("[Çete Sistemi] Test komutları:")
        print("  - !cete, /cete, !gang (chat)")
        print("  - gang_menu (console)")
        print("  - gang_test_ui (console)")
    end)
end
