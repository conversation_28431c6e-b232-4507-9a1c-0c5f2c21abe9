-- Çete Sistemi Yapılandırma Dosyası
GangSystem = GangSystem or {}
GangSystem.Config = {}

-- Çete oluşturabilen meslekler
GangSystem.Config.AllowedCreateJobs = {
    ["mayor"] = true,
    ["Mayor"] = true,
    ["MAYOR"] = true,
    ["gang"] = true,
    ["gangster"] = true,
    ["thief"] = true,
    ["drug_dealer"] = true,
    ["hitman"] = true,
    ["mob"] = true,
    ["mafia"] = true,
}

-- Çeteye katılabilen meslekler
GangSystem.Config.AllowedJoinJobs = {
    ["gang"] = true,
    ["gangster"] = true,
    ["thief"] = true,
    ["drug_dealer"] = true,
    ["hitman"] = true,
    ["mob"] = true,
    ["mafia"] = true,
    ["gun_dealer"] = true,
    ["black_market_dealer"] = true,
    ["kidnapper"] = true,
}

-- Çete ayarları
GangSystem.Config.MaxGangs = 10 -- Maksimum çete sayısı
GangSystem.Config.MaxMembers = 8 -- Çete başına maksimum üye
GangSystem.Config.MinNameLength = 3 -- Minimum çete ismi uzunluğu
GangSystem.Config.MaxNameLength = 20 -- Maksimum çete ismi uzunluğu
GangSystem.Config.CreateCost = 50000 -- Çete oluşturma maliyeti

-- Çete rolleri
GangSystem.Config.Ranks = {
    [1] = {name = "Üye", color = Color(255, 255, 255)},
    [2] = {name = "Yardımcı", color = Color(255, 255, 0)},
    [3] = {name = "Lider", color = Color(255, 0, 0)},
}

-- UI Renkleri (Modern tema)
GangSystem.Config.Colors = {
    Primary = Color(45, 45, 45, 255),
    Secondary = Color(35, 35, 35, 255),
    Accent = Color(0, 150, 255, 255),
    Success = Color(0, 200, 100, 255),
    Warning = Color(255, 150, 0, 255),
    Danger = Color(255, 50, 50, 255),
    Text = Color(255, 255, 255, 255),
    TextSecondary = Color(200, 200, 200, 255),
}

-- Mesajlar
GangSystem.Config.Messages = {
    NoPermission = "Bu işlem için yetkiniz yok!",
    NotInGang = "Bir çetede değilsiniz!",
    AlreadyInGang = "Zaten bir çetedesiniz!",
    GangNotFound = "Çete bulunamadı!",
    InvalidJob = "Bu meslek çete oluşturamaz!",
    InvalidJoinJob = "Bu meslek çeteye katılamaz!",
    NotEnoughMoney = "Yeterli paranız yok!",
    GangCreated = "Çete başarıyla oluşturuldu!",
    GangDeleted = "Çete silindi!",
    PlayerInvited = "Oyuncu çeteye davet edildi!",
    PlayerKicked = "Oyuncu çeteden atıldı!",
    InviteReceived = "Çete davetiniz var!",
    LeaderDied = "Çete lideri öldü, çete kapatıldı!",
    LeaderLeft = "Çete lideri sunucudan ayrıldı, çete kapatıldı!",
    JobChanged = "Meslek değişikliği nedeniyle çeteden atıldınız!",
}
