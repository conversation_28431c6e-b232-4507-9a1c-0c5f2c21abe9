-- Çete Sistemi Test Dosyası
-- Bu dosyayı sunucu console'unda çalıştırarak sistemi test edebilirsiniz

print("=== Çete Sistemi Test Başlatılıyor ===")

-- Test fonksiyonları
local function TestGangCreation()
    print("\n--- Çete Oluşturma Testi ---")
    
    -- Test oyuncusu bul
    local testPlayer = nil
    for _, ply in ipairs(player.GetAll()) do
        if IsValid(ply) then
            testPlayer = ply
            break
        end
    end
    
    if not testPlayer then
        print("Test için oyuncu bulunamadı!")
        return false
    end
    
    print("Test oyuncusu: " .. testPlayer:Nick())
    
    -- Çete oluşturmayı test et
    local success, msg = GangSystem.CreateGang(testPlayer, "Test Çetesi")
    print("Çete oluşturma sonucu: " .. tostring(success) .. " - " .. msg)
    
    return success
end

local function TestGangList()
    print("\n--- Çete Listesi ---")
    
    if table.Count(GangSystem.Gangs) == 0 then
        print("Hiç çete yok!")
        return
    end
    
    for id, gang in pairs(GangSystem.Gangs) do
        print(string.format("ID: %d, İsim: %s, Üye Sayısı: %d", 
            id, gang.name, table.Count(gang.members)))
    end
end

local function TestMemoryStorage()
    print("\n--- Bellek Depolama Testi ---")

    print("Bellekte " .. table.Count(GangSystem.Gangs) .. " çete var")
    print("Aktif davet sayısı: " .. table.Count(GangSystem.PlayerInvites))
    print("Sonraki çete ID: " .. GangSystem.NextGangID)

    if table.Count(GangSystem.Gangs) > 0 then
        print("Çete detayları:")
        for id, gang in pairs(GangSystem.Gangs) do
            print(string.format("  ID: %d, İsim: %s, Lider: %s, Üye: %d",
                id, gang.name, gang.leader, table.Count(gang.members)))
        end
    end
end

local function TestConfig()
    print("\n--- Yapılandırma Testi ---")
    
    print("Maksimum çete sayısı: " .. GangSystem.Config.MaxGangs)
    print("Maksimum üye sayısı: " .. GangSystem.Config.MaxMembers)
    print("Çete oluşturma maliyeti: " .. DarkRP.formatMoney(GangSystem.Config.CreateCost))
    
    print("Çete oluşturabilen meslekler:")
    for job, allowed in pairs(GangSystem.Config.AllowedCreateJobs) do
        if allowed then
            print("  - " .. job)
        end
    end

    print("Çeteye katılabilen meslekler:")
    for job, allowed in pairs(GangSystem.Config.AllowedJoinJobs) do
        if allowed then
            print("  - " .. job)
        end
    end
end

local function TestPlayerPermissions()
    print("\n--- Oyuncu Yetki Testi ---")
    
    for _, ply in ipairs(player.GetAll()) do
        if IsValid(ply) then
            local canCreate = GangSystem.CanCreateGang(ply)
            local canJoin = GangSystem.CanJoinGang(ply)
            local hasGang = GangSystem.HasGang(ply)
            local job = ply:getDarkRPVar("job") or "Bilinmiyor"

            print(string.format("%s - Meslek: %s, Oluşturabilir: %s, Katılabilir: %s, Çetesi Var: %s",
                ply:Nick(), job, tostring(canCreate), tostring(canJoin), tostring(hasGang)))
        end
    end
end

local function RunAllTests()
    print("=== TÜM TESTLER BAŞLATIYOR ===")
    
    TestConfig()
    TestMemoryStorage()
    TestPlayerPermissions()
    TestGangList()
    
    -- Çete oluşturma testini sadece gerekirse çalıştır
    if table.Count(GangSystem.Gangs) == 0 then
        TestGangCreation()
        TestGangList() -- Yeniden listele
    end
    
    print("\n=== TESTLER TAMAMLANDI ===")
end

-- Console komutları
concommand.Add("gang_test_all", function(ply, cmd, args)
    if IsValid(ply) and not ply:IsSuperAdmin() then
        ply:ChatPrint("Bu komutu sadece süper adminler kullanabilir!")
        return
    end
    
    RunAllTests()
end)

concommand.Add("gang_test_config", function(ply, cmd, args)
    if IsValid(ply) and not ply:IsSuperAdmin() then
        ply:ChatPrint("Bu komutu sadece süper adminler kullanabilir!")
        return
    end
    
    TestConfig()
end)

concommand.Add("gang_test_memory", function(ply, cmd, args)
    if IsValid(ply) and not ply:IsSuperAdmin() then
        ply:ChatPrint("Bu komutu sadece süper adminler kullanabilir!")
        return
    end

    TestMemoryStorage()
end)

concommand.Add("gang_test_perms", function(ply, cmd, args)
    if IsValid(ply) and not ply:IsSuperAdmin() then
        ply:ChatPrint("Bu komutu sadece süper adminler kullanabilir!")
        return
    end

    TestPlayerPermissions()
end)

-- Belirli bir oyuncunun meslek bilgilerini kontrol et
concommand.Add("gang_check_job", function(ply, cmd, args)
    if IsValid(ply) and not ply:IsSuperAdmin() then
        ply:ChatPrint("Bu komutu sadece süper adminler kullanabilir!")
        return
    end

    local targetName = args[1]
    if not targetName then
        print("Kullanım: gang_check_job <oyuncu_adı>")
        return
    end

    local target = nil
    for _, p in ipairs(player.GetAll()) do
        if string.find(string.lower(p:Nick()), string.lower(targetName)) then
            target = p
            break
        end
    end

    if not target then
        print("Oyuncu bulunamadı: " .. targetName)
        return
    end

    print("=== " .. target:Nick() .. " Meslek Bilgileri ===")
    local job = target:getDarkRPVar("job") or target:Team()
    local jobTable = RPExtraTeams[job]

    if jobTable then
        print("Job ID: " .. tostring(job))
        print("Job Command: " .. tostring(jobTable.command))
        print("Job Name: " .. tostring(jobTable.name))
        print("Çete Oluşturabilir: " .. tostring(GangSystem.CanCreateGang(target)))
        print("Çeteye Katılabilir: " .. tostring(GangSystem.CanJoinGang(target)))
    else
        print("JobTable bulunamadı!")
    end
end)

-- Otomatik test (sunucu başladıktan 5 saniye sonra)
timer.Simple(5, function()
    if GangSystem and GangSystem.Config then
        print("\n[Çete Sistemi] Otomatik test başlatılıyor...")
        TestConfig()
        TestMemoryStorage()
        print("[Çete Sistemi] Otomatik test tamamlandı!")
        print("[Çete Sistemi] Daha detaylı test için 'gang_test_all' komutunu kullanın")
    end
end)

print("=== Test Komutları ===")
print("gang_test_all - Tüm testleri çalıştır")
print("gang_test_config - Yapılandırma testini çalıştır")
print("gang_test_memory - Bellek depolama testini çalıştır")
print("gang_test_perms - Yetki testini çalıştır")
print("========================")
